import { Download, GraduationCap, Briefcase, Award, Languages } from "lucide-react";
import Navigation from "@/components/Navigation";
import Footer from "@/components/Footer";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import jsPDF from 'jspdf';

const About = () => {
  const generatePDF = () => {
    try {
      const doc = new jsPDF({
        orientation: 'portrait',
        unit: 'mm',
        format: 'a4'
      });
      
      // Set encoding for better Arabic support
      doc.setFont("helvetica");
      
      // Header with name
      doc.setFontSize(18);
      doc.setTextColor(245, 158, 11); // amber color
      doc.text('حذيفه عبدالمعز عبدالرحمان محمد حاتم الحذيفي', 105, 20, { 
        align: 'center'
      });
      
      doc.setFontSize(12);
      doc.setTextColor(100, 100, 100);
      doc.text('مهندس تقنية معلومات', 105, 28, { 
        align: 'center'
      });
      
      // Separator line
      doc.setDrawColor(245, 158, 11);
      doc.setLineWidth(0.5);
      doc.line(20, 32, 190, 32);
      
      // Contact Information Section
      doc.setFontSize(14);
      doc.setTextColor(245, 158, 11);
      doc.text('معلومات التواصل', 20, 42);
      
      doc.setFontSize(10);
      doc.setTextColor(0, 0, 0);
      doc.text('البريد الإلكتروني: <EMAIL>', 20, 50);
      doc.text('الهاتف: +967 777548421 / +967 718706242', 20, 56);
      doc.text('الموقع: عدن، اليمن', 20, 62);
      
      // Professional Summary Section
      doc.setFontSize(14);
      doc.setTextColor(245, 158, 11);
      doc.text('الملخص المهني', 20, 75);
      
      doc.setFontSize(9);
      doc.setTextColor(0, 0, 0);
      const summaryText = `طالب تقنية المعلومات في السنة الرابعة في كلية الهندسة جامعة عدن، لدي خبرة متواضعة في بعض لغات البرمجة
C++, C#, Html, Css, Js, Php, Sql وعملت مسبقا في تطوير عدة برامج ويندوز بسيطة بلغة C# وانتقلت حاليا
إلى مجال الويب وعملت مشروع شبه مكتمل بواسطة بيئة Laravel 11، إضافة إلى خبرة في تصميم قواعد البيانات
وعملت على Sqlserver و Mysql ولدي معرفة في جانب هندسة وتحليل البرمجيات وDesign pattern.`;
      
      const splitSummary = doc.splitTextToSize(summaryText, 170);
      doc.text(splitSummary, 20, 83);
      
      // Education Section
      doc.setFontSize(14);
      doc.setTextColor(245, 158, 11);
      doc.text('التعليم', 20, 115);
      
      doc.setFontSize(10);
      doc.setTextColor(0, 0, 0);
      doc.text('• جامعة عدن - كلية الهندسة', 25, 123);
      doc.text('• بكالوريوس تقنية المعلومات - السنة الرابعة', 25, 129);
      
      // Experience Section
      doc.setFontSize(14);
      doc.setTextColor(245, 158, 11);
      doc.text('الخبرة العملية', 20, 142);
      
      doc.setFontSize(9);
      doc.setTextColor(0, 0, 0);
      doc.text('• عملت كشريك في احدى المعاهد التقنية في تحليل قواعد البيانات لمدرسة في احدى القرى اليمنية', 25, 150);
      doc.text('• أعمل في مجال التجارة الحرة', 25, 156);
      
      // Projects Section
      doc.setFontSize(14);
      doc.setTextColor(245, 158, 11);
      doc.text('المشاريع', 20, 169);
      
      doc.setFontSize(9);
      doc.setTextColor(0, 0, 0);
      doc.text('• برنامج إدارة الحجز - برنامج desktop لإدارة عمليات الحجز', 25, 177);
      doc.text('• متجر إلكتروني بواسطة Laravel 11 للأطفال حديثي الولادة', 25, 183);
      doc.text('• تصميم قواعد البيانات للمدارس والفنادق', 25, 189);
      
      // Skills Section
      doc.setFontSize(14);
      doc.setTextColor(245, 158, 11);
      doc.text('المهارات التقنية', 20, 202);

      doc.setFontSize(8);
      doc.setTextColor(0, 0, 0);
      doc.text('• Frontend: React 18, TypeScript, HTML5, CSS3, JavaScript ES6+', 25, 210);
      doc.text('• Styling: Tailwind CSS, CSS Modules, Responsive Design', 25, 216);
      doc.text('• UI Libraries: Radix UI, Lucide React, Recharts', 25, 222);
      doc.text('• Build Tools: Vite, ESLint, PostCSS, Autoprefixer', 25, 228);
      doc.text('• State Management: React Query (TanStack), React Router DOM', 25, 234);
      doc.text('• Development: Node.js, npm, Git, GitHub Actions', 25, 240);
      doc.text('• Backend: PHP, Laravel, C++, C#', 25, 246);
      doc.text('• Database: SQL Server, MySQL, SQL', 25, 252);
      doc.text('• Design: Figma (UX/UI), Adobe Creative Suite', 25, 258);
      doc.text('• Deployment: GitHub Pages, CI/CD, Environment Variables', 25, 264);
      doc.text('• Office: Microsoft Office Suite, Documentation', 25, 270);
      
      // Languages Section
      doc.setFontSize(14);
      doc.setTextColor(245, 158, 11);
      doc.text('اللغات', 20, 285);

      doc.setFontSize(9);
      doc.setTextColor(0, 0, 0);
      doc.text('• العربية: الأم', 25, 293);
      doc.text('• الإنجليزية: ممتاز', 25, 299);
      doc.text('• الفرنسية: متوسط', 25, 305);
      
      // Courses Section
      if (doc.internal.pageSize.height - 305 < 30) {
        doc.addPage();
        doc.setFontSize(14);
        doc.setTextColor(245, 158, 11);
        doc.text('الدورات', 20, 20);
        
        doc.setFontSize(9);
        doc.setTextColor(0, 0, 0);
        doc.text('• أساسيات البرمجة و C++ - منصة Programming Advices (2022-2024)', 25, 28);
        doc.text('• تطوير تطبيقات سطح المكتب - منصة Programming Advices (2024)', 25, 34);
        doc.text('• تطوير المواقع Frontend - منصة Alzero Web School', 25, 40);
        doc.text('• تطوير المواقع Backend - أكاديمية الجيل العربي', 25, 46);
        doc.text('• اللغة الإنجليزية - معهد أميدست الأمريكي (سنة كاملة)', 25, 52);
        doc.text('• CCNA - سبتمبر 2023', 25, 58);
        doc.text('• CPS - معهد أميديست الأمريكي', 25, 64);
      }
      
      // Generate filename with Arabic text and current date
      const currentDate = new Date().toLocaleDateString('ar-EG');
      const fileName = `حذيفه_الحذيفي_السيرة_الذاتية_${currentDate}.pdf`;
      
      // Save the PDF with Arabic filename
      doc.save(fileName);
      
      console.log('PDF generated successfully');
    } catch (error) {
      console.error('Error generating PDF:', error);
      
      // Fallback: Create a simple text file if PDF generation fails
      const cvContent = `
حذيفه عبدالمعز عبدالرحمان محمد حاتم الحذيفي
مهندس تقنية معلومات

معلومات التواصل:
البريد الإلكتروني: <EMAIL>
الهاتف: +967 777548421 / +967 718706242
الموقع: عدن، اليمن

الملخص المهني:
طالب تقنية المعلومات في السنة الرابعة في كلية الهندسة جامعة عدن، لدي خبرة متواضعة في بعض لغات البرمجة وهي: C++, C#, Html, Css, Js, Php, Sql

التعليم:
• جامعة عدن - كلية الهندسة
• بكالوريوس تقنية المعلومات - السنة الرابعة

المهارات التقنية:
• Frontend: React 18, TypeScript, HTML5, CSS3, JavaScript ES6+
• Styling: Tailwind CSS, CSS Modules, Responsive Design
• UI Libraries: Radix UI, Lucide React, Recharts
• Build Tools: Vite, ESLint, PostCSS, Autoprefixer
• State Management: React Query (TanStack), React Router DOM
• Development: Node.js, npm, Git, GitHub Actions
• Backend: PHP, Laravel, C++, C#
• Database: SQL Server, MySQL, SQL
• Design: Figma (UX/UI), Adobe Creative Suite
• Deployment: GitHub Pages, CI/CD, Environment Variables
• Office: Microsoft Office Suite, Documentation

اللغات:
• العربية: الأم
• الإنجليزية: ممتاز
• الفرنسية: متوسط
      `;
      
      const blob = new Blob([cvContent], { type: 'text/plain;charset=utf-8' });
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = 'حذيفه_الحذيفي_السيرة_الذاتية.txt';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-black via-gray-900 to-black text-white">
      <Navigation />
      
      <div className="pt-20 pb-12">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Header */}
          <div className="text-center mb-12">
            <h1 className="text-4xl lg:text-6xl font-bold mb-4">
              <span className="text-amber-400">السيرة</span> الذاتية
            </h1>
            <div className="w-24 h-1 bg-amber-400 mx-auto mb-8"></div>
            <Button 
              onClick={generatePDF}
              className="bg-amber-500 hover:bg-amber-600 text-black font-semibold px-8 py-3 rounded-full transition-all duration-300"
            >
              <Download className="w-4 h-4 ml-2" />
              تحميل السيرة الذاتية PDF
            </Button>
          </div>

          {/* Personal Info with Profile Photo */}
          <Card className="bg-gray-900/50 border-amber-500/20 mb-8">
            <CardHeader>
              <div className="flex flex-col lg:flex-row items-center gap-8">
                {/* Profile Photo */}
                <div className="relative w-48 h-48 mx-auto lg:mx-0 group">
                  {/* Animated Border */}
                  <div className="absolute inset-0 bg-gradient-to-br from-amber-500/30 to-amber-600/30 rounded-full animate-spin-slow"></div>
                  <div className="absolute inset-2 bg-gradient-to-br from-gray-800 to-gray-900 rounded-full transition-all duration-500"></div>

                  {/* Profile Image */}
                  <div className="absolute inset-4 rounded-full overflow-hidden group-hover:scale-105 transition-transform duration-500">
                    <img
                      src="profile-photo.jpg"
                      alt="حذيفه عبدالمعز الحذيفي - مهندس تقنية معلومات"
                      className="w-full h-full object-cover object-center transition-all duration-500 group-hover:brightness-110"
                      loading="eager"
                    />
                    {/* Overlay Effect */}
                    <div className="absolute inset-0 bg-gradient-to-t from-amber-500/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                  </div>
                </div>

                {/* Personal Information */}
                <div className="text-center lg:text-right flex-1">
                  <CardTitle className="text-2xl text-amber-400 mb-2">
                    حذيفه عبدالمعز عبدالرحمان محمد حاتم الحذيفي
                  </CardTitle>
                  <p className="text-xl text-gray-300 mb-4">مهندس تقنية معلومات</p>

                  {/* Contact Information */}
                  <div className="space-y-2 text-gray-400">
                    <p className="flex items-center justify-center lg:justify-start gap-2">
                      <span>📧</span>
                      <span><EMAIL></span>
                    </p>
                    <p className="flex items-center justify-center lg:justify-start gap-2">
                      <span>📱</span>
                      <span>+967 777548421 | +967 718706242</span>
                    </p>
                    <p className="flex items-center justify-center lg:justify-start gap-2">
                      <span>📍</span>
                      <span>عدن، اليمن</span>
                    </p>
                  </div>
                </div>
              </div>
            </CardHeader>
          </Card>

          <Card className="bg-gray-900/50 border-amber-500/20 mb-8">
            <CardHeader>
              <CardTitle className="text-xl text-amber-400 flex items-center">
                <Briefcase className="w-5 h-5 ml-2" />
                الملخص المهني
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-gray-300 leading-relaxed">
                طالب تقنية المعلومات في السنة الرابعة في كلية الهندسة جامعة عدن، لدي خبرة متواضعة في بعض لغات البرمجة وهي: 
                C++, C#, Html, Css, Js, Php, Sql كما أنني قد عملت مسبقا في تطوير عدة برامج ويندوز بسيطة بلغة C# وقد انتقلت حاليا إلى مجال الويب وعملت مشروع شبه مكتمل بواسطة بيئة Laravel 11 حيث أنني أجري التطوير عليه، إضافة إلى ذلك لدي خبرة متواضعة في تصميم قواعد البيانات وعملت على Sqlserver و Mysql وكذلك لدي معرفة في جانب هندسة وتحليل البرمجيات وأيضا معرفة بسيطة في جانب Design pattern، كما أنني أتطلع إلى التعلم أكثر وتطوير خبرتي العملية بشكل أوسع.
              </p>
            </CardContent>
          </Card>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
            <Card className="bg-gray-900/50 border-amber-500/20">
              <CardHeader>
                <CardTitle className="text-xl text-amber-400 flex items-center">
                  <GraduationCap className="w-5 h-5 ml-2" />
                  التعليم
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div>
                    <h3 className="font-semibold text-gray-200">جامعة عدن - كلية الهندسة</h3>
                    <p className="text-gray-400">طالب بكلاريوس – تقنية المعلومات</p>
                    <p className="text-gray-400">السنة الرابعة</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="bg-gray-900/50 border-amber-500/20">
              <CardHeader>
                <CardTitle className="text-xl text-amber-400 flex items-center">
                  <Languages className="w-5 h-5 ml-2" />
                  اللغات
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-gray-300">العربية</span>
                    <span className="text-amber-400">الأم</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-300">الإنجليزية</span>
                    <span className="text-amber-400">ممتاز</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-300">الفرنسية</span>
                    <span className="text-amber-400">متوسط</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          <Card className="bg-gray-900/50 border-amber-500/20 mb-8">
            <CardHeader>
              <CardTitle className="text-xl text-amber-400 flex items-center">
                <Briefcase className="w-5 h-5 ml-2" />
                الخبرة العملية
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="border-r-2 border-amber-500 pr-4">
                  <p className="text-gray-300">عملت كشريك في احدى المعاهد التقنية في تحليل قواعد البيانات لمدرسة في احدى القرى اليمنية.</p>
                </div>
                <div className="border-r-2 border-amber-500 pr-4">
                  <p className="text-gray-300">كما أنني أعمل في مجال التجارة الحرة.</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-gray-900/50 border-amber-500/20 mb-8">
            <CardHeader>
              <CardTitle className="text-xl text-amber-400">المشاريع</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="border-r-2 border-amber-500 pr-4">
                  <h4 className="font-semibold text-gray-200 mb-2">برنامج إدارة الحجز</h4>
                  <p className="text-gray-300">بناء برنامج desktop حر وبسيط في عمليات الحجز وفكرة البرنامج شاملة لأي جانب يتطلب إدارة الحجز بشكل عام.</p>
                </div>
                <div className="border-r-2 border-amber-500 pr-4">
                  <h4 className="font-semibold text-gray-200 mb-2">متجر إلكتروني - Laravel 11</h4>
                  <p className="text-gray-300">بناء موقع تجاري حر بإستخدام بيئة التعامل Laravel11 وقد خصصت الموقع أن يكون متجر يلبي احتياجات الأطفال حديثي الولادة حتى سن السنتين ويجري التطوير عليه إلى أن يكون الموقع أكثر مرونة لأي جانب تجاري محتمل.</p>
                </div>
                <div className="border-r-2 border-amber-500 pr-4">
                  <h4 className="font-semibold text-gray-200 mb-2">تصميم قواعد البيانات للمدارس</h4>
                  <p className="text-gray-300">عملت كشريك في تحليل وتصميم قواعد بيانات لأحدى مدارس القرى اليمنية</p>
                </div>
                <div className="border-r-2 border-amber-500 pr-4">
                  <h4 className="font-semibold text-gray-200 mb-2">نظام إدارة الفنادق</h4>
                  <p className="text-gray-300">تحليل وتصميم قاعدة بيانات لأحد الفنادق في عدن</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-gray-900/50 border-amber-500/20 mb-8">
            <CardHeader>
              <CardTitle className="text-xl text-amber-400 flex items-center">
                <Award className="w-5 h-5 ml-2" />
                الدورات
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div className="border-r-2 border-amber-500 pr-4">
                  <h4 className="font-semibold text-gray-200">أساسيات البرمجة و C++</h4>
                  <p className="text-gray-400">منصة Programming Advices الأردنية (2022-2024)</p>
                </div>
                <div className="border-r-2 border-amber-500 pr-4">
                  <h4 className="font-semibold text-gray-200">تطوير تطبيقات سطح المكتب</h4>
                  <p className="text-gray-400">منصة Programming Advices الأردنية (2024)</p>
                </div>
                <div className="border-r-2 border-amber-500 pr-4">
                  <h4 className="font-semibold text-gray-200">تطوير المواقع Frontend</h4>
                  <p className="text-gray-400">منصة Alzero Web School المصرية</p>
                </div>
                <div className="border-r-2 border-amber-500 pr-4">
                  <h4 className="font-semibold text-gray-200">تطوير المواقع Backend</h4>
                  <p className="text-gray-400">أكاديمية الجيل العربي - اليمن</p>
                </div>
                <div className="border-r-2 border-amber-500 pr-4">
                  <h4 className="font-semibold text-gray-200">اللغة الإنجليزية</h4>
                  <p className="text-gray-400">معهد أميدست الأمريكي - عدن (سنة كاملة)</p>
                </div>
                <div className="border-r-2 border-amber-500 pr-4">
                  <h4 className="font-semibold text-gray-200">CCNA</h4>
                  <p className="text-gray-400">سبتمبر 2023</p>
                </div>
                <div className="border-r-2 border-amber-500 pr-4">
                  <h4 className="font-semibold text-gray-200">CPS</h4>
                  <p className="text-gray-400">معهد أميديست الأمريكي</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-gray-900/50 border-amber-500/20">
            <CardHeader>
              <CardTitle className="text-xl text-amber-400">المهارات</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <h4 className="font-semibold text-gray-200 mb-2">المهارات التقنية</h4>
                  <ul className="text-gray-300 space-y-1 text-sm">
                    {/* Frontend Technologies */}
                    <li>• <span className="text-amber-400">Frontend:</span> React 18, TypeScript, HTML5, CSS3, JavaScript ES6+</li>
                    <li>• <span className="text-amber-400">Styling:</span> Tailwind CSS, CSS Modules, Responsive Design</li>
                    <li>• <span className="text-amber-400">UI Libraries:</span> Radix UI, Lucide React, Recharts</li>

                    {/* Build Tools & Development */}
                    <li>• <span className="text-amber-400">Build Tools:</span> Vite, ESLint, PostCSS, Autoprefixer</li>
                    <li>• <span className="text-amber-400">State Management:</span> React Query (TanStack), React Router DOM</li>
                    <li>• <span className="text-amber-400">Development:</span> Node.js, npm, Git, GitHub Actions</li>

                    {/* Backend & Database */}
                    <li>• <span className="text-amber-400">Backend:</span> PHP, Laravel, C++, C#</li>
                    <li>• <span className="text-amber-400">Database:</span> SQL Server, MySQL, SQL</li>

                    {/* Tools & Deployment */}
                    <li>• <span className="text-amber-400">Design:</span> Figma (UX/UI), Adobe Creative Suite</li>
                    <li>• <span className="text-amber-400">Deployment:</span> GitHub Pages, CI/CD, Environment Variables</li>
                    <li>• <span className="text-amber-400">Office:</span> Microsoft Office Suite, Documentation</li>
                  </ul>
                </div>
                <div>
                  <h4 className="font-semibold text-gray-200 mb-2">المهارات الشخصية</h4>
                  <ul className="text-gray-300 space-y-1">
                    <li>• سريع التعلم</li>
                    <li>• مهارة التواصل</li>
                    <li>• العمل ضمن فريق</li>
                    <li>• مواكبة أي تقنية جديدة</li>
                  </ul>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      <Footer />
    </div>
  );
};

export default About;
